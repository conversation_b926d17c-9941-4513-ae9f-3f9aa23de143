# Componentes Modal

Esta carpeta contiene todos los componentes de modal reutilizables de la aplicación.

## Estructura

```
modal/
├── index.ts                 # Exportaciones centralizadas
├── AgricultorModal.tsx      # Modal para gestión de agricultores
├── README.md               # Este archivo
└── [FuturoModal].tsx       # Futuros modales
```

## Uso

### Importación Centralizada
```typescript
import { AgricultorModal } from "../../components/modal";
```

### Importación Directa
```typescript
import AgricultorModal from "../../components/modal/AgricultorModal";
```

## Convenciones

### Nomenclatura
- Los archivos de modal deben seguir el patrón: `[Entidad]Modal.tsx`
- Ejemplos: `AgricultorModal.tsx`, `EstablecimientoModal.tsx`, `UsuarioModal.tsx`

### Props Interface
Cada modal debe tener una interface de props que incluya:
```typescript
interface [Entidad]ModalProps {
  open: boolean;
  onClose: (event?: React.MouseEvent<HTMLElement>, reason?: string) => void;
  onSubmit: (formData: any) => void;
  estadoModal: "add" | "update";
  initialData?: [Entidad] | null;
}
```

### Estructura del Modal
1. **Validaciones**: Funciones de validación por pestaña/sección
2. **Estados**: useState para formulario, errores, pestañas, etc.
3. **Memoización**: useMemo para arrays estáticos y objetos de estilo
4. **Callbacks**: useCallback para funciones de manejo de eventos
5. **Render**: JSX con pestañas organizadas y validaciones visuales

## Agregar Nuevos Modales

1. Crear el archivo `[Entidad]Modal.tsx` en esta carpeta
2. Implementar la interface de props estándar
3. Agregar la exportación en `index.ts`:
   ```typescript
   export { default as [Entidad]Modal } from './[Entidad]Modal';
   ```
4. Usar en las páginas:
   ```typescript
   import { [Entidad]Modal } from "../../components/modal";
   ```

## Modales Existentes

### AgricultorModal
- **Propósito**: Gestión de agricultores/ganaderos
- **Pestañas**: 3 (Propietario, Empresa, Contacto)
- **Validaciones**: Formato de documentos, teléfonos, emails
- **Características**: Navegación progresiva entre pestañas, validación en tiempo real

## Beneficios de esta Estructura

1. **Reutilización**: Modales centralizados y reutilizables
2. **Mantenimiento**: Fácil localización y actualización
3. **Consistencia**: Patrones uniformes entre modales
4. **Escalabilidad**: Fácil adición de nuevos modales
5. **Importaciones**: Sistema centralizado de importaciones
