{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/agricultor", "regex": "^/agricultor(?:/)?$", "routeKeys": {}, "namedRegex": "^/agricultor(?:/)?$"}, {"page": "/auth/container", "regex": "^/auth/container(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/container(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/auth/recuperar", "regex": "^/auth/recuperar(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/recuperar(?:/)?$"}, {"page": "/auth/sign", "regex": "^/auth/sign(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/sign(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/documentos", "regex": "^/documentos(?:/)?$", "routeKeys": {}, "namedRegex": "^/documentos(?:/)?$"}, {"page": "/establecimiento", "regex": "^/establecimiento(?:/)?$", "routeKeys": {}, "namedRegex": "^/establecimiento(?:/)?$"}, {"page": "/graficos", "regex": "^/graficos(?:/)?$", "routeKeys": {}, "namedRegex": "^/graficos(?:/)?$"}, {"page": "/insumo", "regex": "^/insumo(?:/)?$", "routeKeys": {}, "namedRegex": "^/insumo(?:/)?$"}, {"page": "/servicio", "regex": "^/servicio(?:/)?$", "routeKeys": {}, "namedRegex": "^/servicio(?:/)?$"}, {"page": "/tareas", "regex": "^/tareas(?:/)?$", "routeKeys": {}, "namedRegex": "^/tareas(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}